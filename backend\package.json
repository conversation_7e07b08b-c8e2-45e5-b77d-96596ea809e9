{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon index.js", "start": "node index.js", "build": "echo 'No build step required'"}, "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.1.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.2", "nodemailer": "^7.0.5", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.10"}}