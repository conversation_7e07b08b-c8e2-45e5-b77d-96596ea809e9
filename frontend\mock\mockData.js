export const countryRules = [
  {
    "iso": "AW",
    "name": "Aruba",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "AF",
    "name": "Afghanistan",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "AO",
    "name": "Angola",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "AI",
    "name": "Anguilla",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "AX",
    "name": "\u00c5land Islands",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "AL",
    "name": "Albania",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "AD",
    "name": "Andorra",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "AE",
    "name": "United Arab Emirates",
    "code": "+971",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "AR",
    "name": "Argentina",
    "code": "+54",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "AM",
    "name": "Armenia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "AS",
    "name": "American Samoa",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "AQ",
    "name": "Antarctica",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "TF",
    "name": "French Southern Territories",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "AG",
    "name": "Antigua and Barbuda",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "AU",
    "name": "Australia",
    "code": "+61",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "AT",
    "name": "Austria",
    "code": "+43",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "AZ",
    "name": "Azerbaijan",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BI",
    "name": "Burundi",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BE",
    "name": "Belgium",
    "code": "+32",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BJ",
    "name": "Benin",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BQ",
    "name": "Bonaire, Sint Eustatius and Saba",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BF",
    "name": "Burkina Faso",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BD",
    "name": "Bangladesh",
    "code": "+880",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BG",
    "name": "Bulgaria",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BH",
    "name": "Bahrain",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BS",
    "name": "Bahamas",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BA",
    "name": "Bosnia and Herzegovina",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BL",
    "name": "Saint Barth\u00e9lemy",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BY",
    "name": "Belarus",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BZ",
    "name": "Belize",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BM",
    "name": "Bermuda",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BO",
    "name": "Bolivia, Plurinational State of",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BR",
    "name": "Brazil",
    "code": "+55",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BB",
    "name": "Barbados",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BN",
    "name": "Brunei Darussalam",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BT",
    "name": "Bhutan",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BV",
    "name": "Bouvet Island",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "BW",
    "name": "Botswana",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CF",
    "name": "Central African Republic",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CA",
    "name": "Canada",
    "code": "+1",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CC",
    "name": "Cocos (Keeling) Islands",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CH",
    "name": "Switzerland",
    "code": "+41",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CL",
    "name": "Chile",
    "code": "+56",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CN",
    "name": "China",
    "code": "+86",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CI",
    "name": "C\u00f4te d'Ivoire",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CM",
    "name": "Cameroon",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CD",
    "name": "Congo, The Democratic Republic of the",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CG",
    "name": "Congo",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CK",
    "name": "Cook Islands",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CO",
    "name": "Colombia",
    "code": "+57",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "KM",
    "name": "Comoros",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CV",
    "name": "Cabo Verde",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CR",
    "name": "Costa Rica",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CU",
    "name": "Cuba",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CW",
    "name": "Cura\u00e7ao",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CX",
    "name": "Christmas Island",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "KY",
    "name": "Cayman Islands",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CY",
    "name": "Cyprus",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "CZ",
    "name": "Czechia",
    "code": "+420",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "DE",
    "name": "Germany",
    "code": "+49",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "DJ",
    "name": "Djibouti",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "DM",
    "name": "Dominica",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "DK",
    "name": "Denmark",
    "code": "+45",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "DO",
    "name": "Dominican Republic",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "DZ",
    "name": "Algeria",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "EC",
    "name": "Ecuador",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "EG",
    "name": "Egypt",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "ER",
    "name": "Eritrea",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "EH",
    "name": "Western Sahara",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "ES",
    "name": "Spain",
    "code": "+34",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "EE",
    "name": "Estonia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "ET",
    "name": "Ethiopia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "FI",
    "name": "Finland",
    "code": "+358",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "FJ",
    "name": "Fiji",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "FK",
    "name": "Falkland Islands (Malvinas)",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "FR",
    "name": "France",
    "code": "+33",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "FO",
    "name": "Faroe Islands",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "FM",
    "name": "Micronesia, Federated States of",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GA",
    "name": "Gabon",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GB",
    "name": "United Kingdom",
    "code": "+44",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GE",
    "name": "Georgia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GG",
    "name": "Guernsey",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GH",
    "name": "Ghana",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GI",
    "name": "Gibraltar",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GN",
    "name": "Guinea",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GP",
    "name": "Guadeloupe",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GM",
    "name": "Gambia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GW",
    "name": "Guinea-Bissau",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GQ",
    "name": "Equatorial Guinea",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GR",
    "name": "Greece",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GD",
    "name": "Grenada",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GL",
    "name": "Greenland",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GT",
    "name": "Guatemala",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GF",
    "name": "French Guiana",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GU",
    "name": "Guam",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GY",
    "name": "Guyana",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "HK",
    "name": "Hong Kong",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "HM",
    "name": "Heard Island and McDonald Islands",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "HN",
    "name": "Honduras",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "HR",
    "name": "Croatia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "HT",
    "name": "Haiti",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "HU",
    "name": "Hungary",
    "code": "+36",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "ID",
    "name": "Indonesia",
    "code": "+62",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "IM",
    "name": "Isle of Man",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "IN",
    "name": "India",
    "code": "+91",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "IO",
    "name": "British Indian Ocean Territory",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "IE",
    "name": "Ireland",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "IR",
    "name": "Iran, Islamic Republic of",
    "code": "+98",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "IQ",
    "name": "Iraq",
    "code": "+964",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "IS",
    "name": "Iceland",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "IL",
    "name": "Israel",
    "code": "+972",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "IT",
    "name": "Italy",
    "code": "+39",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "JM",
    "name": "Jamaica",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "JE",
    "name": "Jersey",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "JO",
    "name": "Jordan",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "JP",
    "name": "Japan",
    "code": "+81",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "KZ",
    "name": "Kazakhstan",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "KE",
    "name": "Kenya",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "KG",
    "name": "Kyrgyzstan",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "KH",
    "name": "Cambodia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "KI",
    "name": "Kiribati",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "KN",
    "name": "Saint Kitts and Nevis",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "KR",
    "name": "Korea, Republic of",
    "code": "+82",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "KW",
    "name": "Kuwait",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "LA",
    "name": "Lao People's Democratic Republic",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "LB",
    "name": "Lebanon",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "LR",
    "name": "Liberia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "LY",
    "name": "Libya",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "LC",
    "name": "Saint Lucia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "LI",
    "name": "Liechtenstein",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "LK",
    "name": "Sri Lanka",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "LS",
    "name": "Lesotho",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "LT",
    "name": "Lithuania",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "LU",
    "name": "Luxembourg",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "LV",
    "name": "Latvia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MO",
    "name": "Macao",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MF",
    "name": "Saint Martin (French part)",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MA",
    "name": "Morocco",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MC",
    "name": "Monaco",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MD",
    "name": "Moldova, Republic of",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MG",
    "name": "Madagascar",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MV",
    "name": "Maldives",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MX",
    "name": "Mexico",
    "code": "+52",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MH",
    "name": "Marshall Islands",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MK",
    "name": "North Macedonia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "ML",
    "name": "Mali",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MT",
    "name": "Malta",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MM",
    "name": "Myanmar",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "ME",
    "name": "Montenegro",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MN",
    "name": "Mongolia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MP",
    "name": "Northern Mariana Islands",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MZ",
    "name": "Mozambique",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MR",
    "name": "Mauritania",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MS",
    "name": "Montserrat",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MQ",
    "name": "Martinique",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MU",
    "name": "Mauritius",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MW",
    "name": "Malawi",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "MY",
    "name": "Malaysia",
    "code": "+60",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "YT",
    "name": "Mayotte",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "NA",
    "name": "Namibia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "NC",
    "name": "New Caledonia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "NE",
    "name": "Niger",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "NF",
    "name": "Norfolk Island",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "NG",
    "name": "Nigeria",
    "code": "+234",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "NI",
    "name": "Nicaragua",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "NU",
    "name": "Niue",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "NL",
    "name": "Netherlands",
    "code": "+31",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "NO",
    "name": "Norway",
    "code": "+47",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "NP",
    "name": "Nepal",
    "code": "+977",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "NR",
    "name": "Nauru",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "NZ",
    "name": "New Zealand",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "OM",
    "name": "Oman",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "PK",
    "name": "Pakistan",
    "code": "+92",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "PA",
    "name": "Panama",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "PN",
    "name": "Pitcairn",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "PE",
    "name": "Peru",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "PH",
    "name": "Philippines",
    "code": "+63",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "PW",
    "name": "Palau",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "PG",
    "name": "Papua New Guinea",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "PL",
    "name": "Poland",
    "code": "+48",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "PR",
    "name": "Puerto Rico",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "KP",
    "name": "Korea, Democratic People's Republic of",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "PT",
    "name": "Portugal",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "PY",
    "name": "Paraguay",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "PS",
    "name": "Palestine, State of",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "PF",
    "name": "French Polynesia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "QA",
    "name": "Qatar",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "RE",
    "name": "R\u00e9union",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "RO",
    "name": "Romania",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "RU",
    "name": "Russian Federation",
    "code": "+7",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "RW",
    "name": "Rwanda",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SA",
    "name": "Saudi Arabia",
    "code": "+966",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SD",
    "name": "Sudan",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SN",
    "name": "Senegal",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SG",
    "name": "Singapore",
    "code": "+65",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "GS",
    "name": "South Georgia and the South Sandwich Islands",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SH",
    "name": "Saint Helena, Ascension and Tristan da Cunha",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SJ",
    "name": "Svalbard and Jan Mayen",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SB",
    "name": "Solomon Islands",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SL",
    "name": "Sierra Leone",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SV",
    "name": "El Salvador",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SM",
    "name": "San Marino",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SO",
    "name": "Somalia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "PM",
    "name": "Saint Pierre and Miquelon",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "RS",
    "name": "Serbia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SS",
    "name": "South Sudan",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "ST",
    "name": "Sao Tome and Principe",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SR",
    "name": "Suriname",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SK",
    "name": "Slovakia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SI",
    "name": "Slovenia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SE",
    "name": "Sweden",
    "code": "+46",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SZ",
    "name": "Eswatini",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SX",
    "name": "Sint Maarten (Dutch part)",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SC",
    "name": "Seychelles",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "SY",
    "name": "Syrian Arab Republic",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "TC",
    "name": "Turks and Caicos Islands",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "TD",
    "name": "Chad",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "TG",
    "name": "Togo",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "TH",
    "name": "Thailand",
    "code": "+66",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "TJ",
    "name": "Tajikistan",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "TK",
    "name": "Tokelau",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "TM",
    "name": "Turkmenistan",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "TL",
    "name": "Timor-Leste",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "TO",
    "name": "Tonga",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "TT",
    "name": "Trinidad and Tobago",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "TN",
    "name": "Tunisia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "TR",
    "name": "Turkey",
    "code": "+90",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "TV",
    "name": "Tuvalu",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "TW",
    "name": "Taiwan, Province of China",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "TZ",
    "name": "Tanzania, United Republic of",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "UG",
    "name": "Uganda",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "UA",
    "name": "Ukraine",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "UM",
    "name": "United States Minor Outlying Islands",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "UY",
    "name": "Uruguay",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "US",
    "name": "United States",
    "code": "+1",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "UZ",
    "name": "Uzbekistan",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "VA",
    "name": "Holy See (Vatican City State)",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "VC",
    "name": "Saint Vincent and the Grenadines",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "VE",
    "name": "Venezuela, Bolivarian Republic of",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "VG",
    "name": "Virgin Islands, British",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "VI",
    "name": "Virgin Islands, U.S.",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "VN",
    "name": "Viet Nam",
    "code": "+84",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "VU",
    "name": "Vanuatu",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "WF",
    "name": "Wallis and Futuna",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "WS",
    "name": "Samoa",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "YE",
    "name": "Yemen",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "ZA",
    "name": "South Africa",
    "code": "+27",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "ZM",
    "name": "Zambia",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  },
  {
    "iso": "ZW",
    "name": "Zimbabwe",
    "code": "",
    "length": 10,
    "regex": "^\\d{6,15}$"
  }
]