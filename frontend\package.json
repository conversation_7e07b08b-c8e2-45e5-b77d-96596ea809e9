{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start -p 4000", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@reduxjs/toolkit": "^2.6.1", "@shadcn/ui": "^0.0.4", "axios": "^1.8.4", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "file-saver": "^2.0.5", "lucide-react": "^0.503.0", "next": "^15.3.1", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "recharts": "^2.15.2", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.8"}, "devDependencies": {"@tailwindcss/postcss": "^4", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^4.1.4"}}